package com.birdeye.social.service;


import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialPagesAuditRepo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.BusinessLocationLiteDTOForGMB;
import com.birdeye.social.dto.LocationDetailsDTO;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.BrokenIntegration;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.SocialPagesAudit;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.platform.dao.BusinessGMBLocationRepository;

import com.birdeye.social.sro.BrokenEnterprisesList;
import com.birdeye.social.sro.EmailAlertDetailResponse;
import com.birdeye.social.sro.EmailAlertResponse;
import com.birdeye.social.sro.GenerateRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service("socialAlertingService")
public class SocialAlertServiceImpl implements SocialAlertService {
    private static Logger logger = LoggerFactory.getLogger(SocialAlertService.class);

    @Autowired
    private IBusinessCoreService businessCoreService;


    @Autowired
    private IBusinessGetPageService businessGetPageService;

    @Autowired
    private SocialAccountService socialAccountService;

    @Autowired
    private SocialPagesAuditRepo socialPagesAuditRepo;

    @Autowired
    BusinessGMBLocationRepository businessGMBLocationRepo;

    @Autowired
    BusinessGMBLocationRawRepository businessGMBLocationRawRepository;

    @Autowired
    BusinessFacebookPageRepository businessFacebookPageRepository;

    @Autowired
    private KafkaProducerService producer;

    @Autowired
    private IBrokenIntegrationService brokenIntegrationService;

    @Autowired
    SocialFBPageRepository socialFBPageRepository;
    
    @Autowired
    private OpenUrlService openUrlService;
    
    @Autowired
	private GMBLocationDetailService gmbLocationDetailService;
    
    @Autowired
	private FacebookPageService				facebookPageService;

    @Autowired
    private ISocialEsService socialEsService;

    private static final String[] sourceArray = {"GMB","FACEBOOK"};

    private static final String PAGES_MESSAGING = "pages_messaging";



    private List<SocialPagesAudit> fetchSocialPageData(String source, List<Integer> businessIds) {
        List<SocialPagesAudit> audits = new ArrayList<>();

        if(source.equalsIgnoreCase("GMB")) {
            List<BusinessGoogleMyBusinessLocation> gmbPages = businessGMBLocationRawRepository.findByBusinessIdIn(businessIds);
            gmbPages.stream().forEach(googleMyBusinessLocation -> {
                SocialPagesAudit audit = new SocialPagesAudit();
                audit.setBusinessId(googleMyBusinessLocation.getBusinessId());
                audit.setIsValid(googleMyBusinessLocation.getIsValid());
                //Manvi: TODO Add check for messenger in code for GMB post release
                audit.setChannel("GMB");
                audits.add(audit);
            });
        } else if(source.equalsIgnoreCase("FACEBOOK")) {
            List<BusinessFBPage> fBPages = socialFBPageRepository.findByBusinessIdIn(businessIds);
            fBPages.stream().forEach(fbPage -> {
                SocialPagesAudit audit = new SocialPagesAudit();
                audit.setBusinessId(fbPage.getBusinessId());
                audit.setIsValid(fbPage.getIsValid());
                audit.setChannel("FACEBOOK");
                audit.setPagePermissions(fbPage.getPagePermissions());
                audits.add(audit);
            });
        }
        return audits;
    }

    private void batchingAlertResponse(EmailAlertResponse data, String type) {
        logger.info("Pushing batches of data for enterprise: {}",data.getBusinessNumber());
        Integer batchSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("email.alert.batch.size", 500);
        final Boolean[] doBatching = {false};
        Map<String,Integer> totalBatches =  new HashMap<>();
        Map<String,List<Integer>> sourceWiseIdsList =  new HashMap<>();
        Map<String,List<Integer>> sourceWiseBrokenIdsList =  new HashMap<>();
        final int[] maxBatches = {0};
        data.getDetails().stream().forEach(details->{
            int batches = (int)Math.ceil(details.getTotal()/batchSize);
            if(details.getTotal()%batchSize != 0) {
                batches = batches + 1;
            }
            logger.info("number of batches: {} for source: {}",batches,details.getIntegrationSource());
            if(details.getTotal()>batchSize) {
                doBatching[0] = true;
                if(maxBatches[0] < batches) {
                    maxBatches[0] = batches;
                }
            }
            logger.info("maxBatches: {} for source: {}",maxBatches[0],details.getIntegrationSource());
            totalBatches.put(details.getIntegrationSource().equals("Google") ? "GMB" : details.getIntegrationSource().toUpperCase(),batches);
            sourceWiseIdsList.put(details.getIntegrationSource().equals("Google") ? "GMB" : details.getIntegrationSource().toUpperCase(),details.getTotalIds());
            sourceWiseBrokenIdsList.put(details.getIntegrationSource().equals("Google") ? "GMB" : details.getIntegrationSource().toUpperCase(),details.getBrokenIds());
        });
        logger.info("Total number of batches: {} for enterprise: {}",maxBatches[0],data.getBusinessNumber());
        if(doBatching[0]== false) {
            data.setStatus(GMBLocationJobStatus.COMPLETE.name());
            producer.sendWithKey(type.equalsIgnoreCase(SocialAlertTypeEnum.WEEKLY.name())?
                            Constants.BROKEN_INTEGRATION_EVENT_BUSINESS_STATUS_TOPIC_NAME:Constants.FIRST_BROKEN_INTEGRATION_TOPIC, data.getBusinessNumber().toString(), data);
        } else {
            logger.info("Starting batching");
            for(int i=0;i<maxBatches[0];i++) {
                EmailAlertResponse pushResponse = data;
                pushResponse.setStatus(i<maxBatches[0]-1?GMBLocationJobStatus.IN_PROGRESS.name():GMBLocationJobStatus.COMPLETE.name());
                for(int j = 0; j < sourceArray.length ; j++) {
                    if(totalBatches.get(sourceArray[j]) != null && totalBatches.get(sourceArray[j]) > i) {
                        if (pushResponse.getDetails().get(j).getIntegrationSource().equalsIgnoreCase(sourceArray[j]) ||
                                sourceArray[j].equalsIgnoreCase("GMB") &&
                                        pushResponse.getDetails().get(j).getIntegrationSource().equalsIgnoreCase("GOOGLE")) {
                            Integer totalArrSize = sourceWiseIdsList.get(sourceArray[j]).size();
                            Integer startOffset = i * batchSize;
                            Integer endOffset = ((i*batchSize) + batchSize)<totalArrSize?(i*batchSize) + batchSize:totalArrSize;
                            logger.info("start Index: {}, end index: {}",startOffset,endOffset);
                            pushResponse.getDetails().get(j).setTotalIds(sourceWiseIdsList.get(sourceArray[j]).subList(
                                    startOffset,endOffset
                            ));
                            logger.info("Creating batch of total ids: {} for source: {} and batch offset: {}"
                                    ,pushResponse.getDetails().get(j).getTotalIds().size(), sourceArray[j],(i+1));
                            List<Integer> finalBrokenIds = new ArrayList<>();
                            //saving broken ids based on batched total ids
                            for (Integer sourceWiseBrokenId : sourceWiseBrokenIdsList.get(sourceArray[j])) {
                                if (pushResponse.getDetails().get(j).getTotalIds().contains(sourceWiseBrokenId)) {
                                    finalBrokenIds.add(sourceWiseBrokenId);
                                }
                            }
                            pushResponse.getDetails().get(j).setBrokenIds(finalBrokenIds);
                            pushResponse.getDetails().get(j).setBroken(finalBrokenIds.size());
                            pushResponse.getDetails().get(j).setTotal(pushResponse.getDetails().get(j).getTotalIds().size());
                        }
                    } else if(pushResponse.getDetails().size()>j) {
                        pushResponse.getDetails().remove(j);
                    }
                }
                producer.sendWithKey(type.equalsIgnoreCase(SocialAlertTypeEnum.WEEKLY.name())?
                                Constants.BROKEN_INTEGRATION_EVENT_BUSINESS_STATUS_TOPIC_NAME:Constants.FIRST_BROKEN_INTEGRATION_TOPIC, data.getBusinessNumber().toString(), data);
            }
        }
        brokenIntegrationService.updateStatus(GMBLocationJobStatus.COMPLETE.name(),data.getBusinessNumber());
    }
    private List<SocialPagesAudit> generateSocialAuditPages( List<SocialElasticDto> audits,String source) {
        List<SocialPagesAudit> response = new ArrayList<>();
        audits.stream().forEach(audit->{
            SocialPagesAudit data = new SocialPagesAudit();
            data.setBusinessId(Integer.parseInt(audit.getBusiness_id()));
            data.setIsValid(audit.getIs_valid());
            data.setChannel(source);
            response.add(data);
        });
        return response;
    }

    public EmailAlertResponse getAllUserDataForEmail(Long enterpriseId, String type) {
        EmailAlertResponse response = new EmailAlertResponse();
        response.setBusinessNumber(enterpriseId);
        response.setServiceName("SOCIAL");
        try {
            logger.info("Generating broken integration data for enterprise: {}", enterpriseId);
            response.setDetails(new ArrayList<>());
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(enterpriseId);
            List<LocationDetailsDTO> locationDetailsDTOS = businessCoreService.getBusinessLocations(businessLiteDTO.getBusinessId());
            List<Integer> businessIds = new ArrayList<>();
            if (CollectionUtils.isEmpty(locationDetailsDTOS)) {
                businessIds = Arrays.asList(businessLiteDTO.getBusinessId());
            } else {
                businessIds = locationDetailsDTOS.stream().map(LocationDetailsDTO::getBusinessId).collect(Collectors.toList());
            }
            List<Integer> userList = new ArrayList<>();
            for (String source : sourceArray) {
                logger.info("Fetching data for source: {} for broken integration data for enterprise: {}", source, enterpriseId);
                EmailAlertDetailResponse res = new EmailAlertDetailResponse();
                res.setIsSocial(true);
                userList.addAll(businessGetPageService.findIdsByEnterpriseIdAndStatusInAndChannel(enterpriseId,
                        Arrays.asList(Status.COMPLETE.getName()), source));
                logger.info("List of social users: {} for source: {} and enterprise: {}", userList, source, enterpriseId);
                List<SocialPagesAudit> audits = new ArrayList<>();
                Integer useEsForFetchData = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("use.ES.to.fetch.data", 500);
                if (useEsForFetchData.equals(1)) {
                    try {
                        List<SocialElasticDto> auditsFromEs = socialEsService.fetchAllMappedPagesForAccount(Arrays.asList(source).stream().map(String::toLowerCase).toArray(String[]::new),businessIds.stream().map(integer -> integer.toString()).collect(Collectors.toList()));
                    	audits = generateSocialAuditPages(auditsFromEs, source);
                    } catch (Exception e) {
                        logger.error("ES breaking while fetching data for email: {}", e.getMessage());
                        audits = fetchSocialPageData(source, businessIds);
                    }
                } else {
                    audits = fetchSocialPageData(source, businessIds);
                }
                if (CollectionUtils.isNotEmpty(audits)) {
                    List<Integer> totalIds = new ArrayList<>();
                    List<Integer> brokenIds = new ArrayList<>();
                    audits.stream().forEach((audit) -> {
                        if (audit.getBusinessId() != null) {
                            totalIds.add(audit.getBusinessId());
                            if (audit.getIsValid() == 0) {
                                brokenIds.add(audit.getBusinessId());
                            }
                        }
                    });
                    res.setBrokenIds(brokenIds);
                    res.setBroken(brokenIds.size());
                    res.setTotalIds(totalIds);
                    res.setTotal(totalIds.size());
                    res.setIntegrationSource(source.equals("GMB") ? "Google" : source.substring(0, 1).toUpperCase() + source.substring(1).toLowerCase());
                    res.setIntegrationSourceDisplayName(source.equals("GMB") ? "Google" : source.substring(0, 1).toUpperCase() + source.substring(1).toLowerCase());
                    res.setReconnectLink(socialAccountService.getConnectCTA(businessLiteDTO, source.toLowerCase()));
                    response.getDetails().add(res);
                    logger.info("Total no of businesses: {}, broken business: {} for source: {} and enterprise: {}",
                            totalIds.size(), brokenIds.size(), source, enterpriseId);
                }
            }
            List<Integer> finalUserList = userList.stream().distinct().collect(Collectors.toList());
            finalUserList = finalUserList.stream().filter(num -> num != null && num != 0).collect(Collectors.toList());
            response.setUsers(finalUserList);
            if (!type.equalsIgnoreCase(SocialAlertTypeEnum.DASHBOARD.name())) {
                batchingAlertResponse(response, type);
            }
            return response;
        }catch (ExternalAPIException exe) {
            logger.warn("Exception while fetching data from CORE Service: {} for enterprise",exe.getMessage(),enterpriseId);

		}catch (Exception e) {
            logger.error("Exception while fetching email related data: {} for enterprise",e.getMessage(),enterpriseId);
        }
        return null;

    }
    
    private List<Long> fetchBrokenEnterpriseIds(Boolean saveData) throws IOException {
    	List<Long> response = new ArrayList<>();
    	Map<Long, String> brokenBizIdToChannel = new HashMap<>();
    	// fetch broken integration from gmb
    	List<Long> gmbBroken = businessGMBLocationRawRepository.findDistinctEnterpriseIdByIsValid(0);
    	if(CollectionUtils.isNotEmpty(gmbBroken)) {
    		gmbBroken.forEach(g-> brokenBizIdToChannel.put(g, "GMB"));
    	}
    	
    	//fetch broken integrations from fb
    	List<Long> fbBroken = socialFBPageRepository.findDistinctEnterpriseIdByIsValid(0);
    	if(CollectionUtils.isNotEmpty(fbBroken)) {
    		fbBroken.forEach(f-> brokenBizIdToChannel.putIfAbsent(f, "FACEBOOK"));
    	}
    	 logger.info("Found {} broken enterprises in fb + gmb ", CollectionUtils.size(brokenBizIdToChannel));
   
    	if(saveData.equals(true) && MapUtils.isNotEmpty(brokenBizIdToChannel)) {
    		// fetch already mailed brokenIntegrations
    		Set<Long> brokenIntegrations = brokenIntegrationService.fetchInProgessDataWithSource(SocialAlertTypeEnum.WEEKLY.name(), brokenBizIdToChannel.keySet());

    		// filter out already mailed broken integrations
    		List<BrokenIntegration> tobeSavedBusinessId =
    				brokenBizIdToChannel.entrySet().stream()
    				.filter(x -> !brokenIntegrations.contains(x.getKey()))
    				.map(temp -> {
    					BrokenIntegration brokenIntegration = new BrokenIntegration();
    					brokenIntegration.setChannel(temp.getValue());
    					brokenIntegration.setEnterpriseId(temp.getKey());
    					brokenIntegration.setSource(SocialAlertTypeEnum.WEEKLY.name());
    					brokenIntegration.setStatus(GMBLocationJobStatus.IN_PROGRESS.name());
    					return brokenIntegration;
    				})
    				.collect(Collectors.toList());
    		
    		logger.info("Saving {} new broken enterprises for fb + gmb ", CollectionUtils.size(tobeSavedBusinessId));
    		if(!CollectionUtils.isEmpty(tobeSavedBusinessId) ) {
    			brokenIntegrationService.saveAll(tobeSavedBusinessId);
    		}
    	}
    	logger.info("Returning after fetching broken enterprises for fb + gmb from db");
    	response.addAll(brokenBizIdToChannel.keySet());
    	return response;
    }
    
    @Override
    public void submitEventForAllAllBrokenIntegrationEnt() {
    	logger.info("Submitting event to fetch all enterprises with broken integration");
    	producer.sendObject(Constants.FETCH_ENT_BROKEN_INTEGRATIONS_TOPIC, null) ;
    }

    @Override
    public BrokenEnterprisesList getAllBrokenIntegrationEnterprises() throws IOException {
        BrokenEnterprisesList response = new BrokenEnterprisesList();
        response.setServiceName("SOCIAL");
        response.setBusinessNumbers(new ArrayList<>());
        logger.info("Fetching all broken integration enterprises");
        Integer useEsForFetchData = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("use.ES.to.fetch.data", 500);
        if(useEsForFetchData.equals(1)) {
        	 logger.info("Fetching broken integration ids via ES ");
            try {
                List<SocialElasticDto> data = socialEsService.fetchAllIntegratedDisconnectedPages(Arrays.asList(sourceArray).stream().map(String::toLowerCase).toArray(String[]::new));
                Map<Long, String> brokenBizIdToChannel = new HashMap<>();
                data.stream().filter(esData -> Objects.nonNull(esData.getEnterprise_id())).forEach(socialElasticDto -> {
                    brokenBizIdToChannel.put(Long.parseLong(socialElasticDto.getEnterprise_id()),socialElasticDto.getChannel());
                });

                if(MapUtils.isNotEmpty(brokenBizIdToChannel)){
                    Set<Long> enterpriseIds = brokenBizIdToChannel.keySet();
                    response.setBusinessNumbers(enterpriseIds.stream().collect(Collectors.toList()));
                    Set<Long> brokenIntegrations = brokenIntegrationService.fetchInProgessDataWithSource(SocialAlertTypeEnum.WEEKLY.name(), enterpriseIds);
                    List<BrokenIntegration> tobeSavedBusinessId =
                            brokenBizIdToChannel.entrySet().stream()
                                    .filter(x -> !brokenIntegrations.contains(x.getKey()))
                                    .map(temp -> {
                                        BrokenIntegration brokenIntegration = new BrokenIntegration();
                                        brokenIntegration.setChannel(temp.getValue());
                                        brokenIntegration.setEnterpriseId(temp.getKey());
                                        brokenIntegration.setSource(SocialAlertTypeEnum.WEEKLY.name());
                                        brokenIntegration.setStatus(GMBLocationJobStatus.IN_PROGRESS.name());
                                        return brokenIntegration;
                                    })
                                    .collect(Collectors.toList());
                    logger.info("Saving {} new broken enterprises for fb + gmb ", CollectionUtils.size(tobeSavedBusinessId));
                    if(!CollectionUtils.isEmpty(tobeSavedBusinessId) ) {
                        brokenIntegrationService.saveAll(tobeSavedBusinessId);
                    }
                }

            } catch (Exception e) {
                logger.error("Error in fetch data from ES in fetching broken integration: {}", e.getMessage());
                response.setBusinessNumbers(fetchBrokenEnterpriseIds(true));
            }
        } else {
        	 logger.info("Fetching broken integration ids via DB ");
            response.setBusinessNumbers(fetchBrokenEnterpriseIds(true));
        }
        
        List<Long> finalBusinessNumbers = new ArrayList<>();
        Map<String, BusinessLocationLiteDTOForGMB> businessesInBulk =
                businessCoreService.getBusinessesInBulkByBusinessNumber(response.getBusinessNumbers(), true);
        if(MapUtils.isNotEmpty(businessesInBulk)) {
        	 finalBusinessNumbers = businessesInBulk.keySet().stream().map(x -> Long.parseLong(x)).collect(Collectors.toList());
        }
        response.setBusinessNumbers(finalBusinessNumbers);
        producer.sendWithKey(Constants.BROKEN_INTEGRATION_EVENT_OVERALL_STATUS_TOPIC_NAME, response.getServiceName(), response);
        return response;
    }

    @Override
    public List<EmailAlertResponse> getAllUserDataForEmailMultiple(List<Long> enterpriseIds) {
        logger.info("Received request for multiple enterprises to fetch broken integration data: {}",enterpriseIds);
        List<EmailAlertResponse> responses = new ArrayList<>();
        for (Long enterpriseId: enterpriseIds) {
            EmailAlertResponse response = getAllUserDataForEmail(enterpriseId, SocialAlertTypeEnum.DASHBOARD.name());
            if(response != null) {
                responses.add(response);
            }
        }
        return responses;
    }

    @Override
    public List<EmailAlertResponse> getAllUserDataForEmailMultipleOptimized(List<Long> enterpriseIds) {
        logger.info("Received optimized request for multiple enterprises to fetch broken integration data: {}", enterpriseIds);

        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return new ArrayList<>();
        }

        try {
            // Step 1: Bulk fetch business data for all enterprises
            Map<String, BusinessLocationLiteDTOForGMB> businessDataMap =
                businessCoreService.getBusinessesInBulkByBusinessNumber(enterpriseIds, false);

            // Step 2: Collect all business IDs for bulk operations
            List<Integer> allBusinessIds = new ArrayList<>();
            Map<Long, List<Integer>> enterpriseToBusinessIdsMap = new HashMap<>();

            for (Long enterpriseId : enterpriseIds) {
                BusinessLocationLiteDTOForGMB businessData = businessDataMap.get(enterpriseId.toString());
                if (businessData != null) {
                    List<LocationDetailsDTO> locationDetailsDTOS = businessCoreService.getBusinessLocations(businessData.getBusinessId());

                    if (CollectionUtils.isEmpty(locationDetailsDTOS)) {
                        allBusinessIds = Arrays.asList(businessData.getBusinessId());
                    } else {
                        allBusinessIds = locationDetailsDTOS.stream().map(LocationDetailsDTO::getBusinessId).collect(Collectors.toList());
                    }

                    enterpriseToBusinessIdsMap.put(enterpriseId, allBusinessIds);

                }
            }

            // Step 3: Process enterprises in parallel
            List<CompletableFuture<EmailAlertResponse>> futures = enterpriseIds.stream()
                .map(enterpriseId -> CompletableFuture.supplyAsync(() ->
                    processEnterpriseOptimized(enterpriseId, businessDataMap, enterpriseToBusinessIdsMap)))
                .collect(Collectors.toList());

            // Step 4: Collect results
            List<EmailAlertResponse> responses = futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            logger.info("Optimized processing completed for {} enterprises, returned {} responses",
                enterpriseIds.size(), responses.size());
            return responses;

        } catch (Exception e) {
            logger.error("Error in optimized multiple enterprise processing: {}", e.getMessage(), e);
            // Fallback to original method
            return getAllUserDataForEmailMultiple(enterpriseIds);
        }
    }

    /**
     * Optimized processing for a single enterprise using pre-fetched bulk data
     */
    private EmailAlertResponse processEnterpriseOptimized(Long enterpriseId,
            Map<String, BusinessLocationLiteDTOForGMB> businessDataMap,
            Map<Long, List<Integer>> enterpriseToBusinessIdsMap) {

        EmailAlertResponse response = new EmailAlertResponse();
        response.setBusinessNumber(enterpriseId);
        response.setServiceName("SOCIAL");

        try {
            logger.info("Processing enterprise optimized: {}", enterpriseId);
            response.setDetails(new ArrayList<>());

            BusinessLocationLiteDTOForGMB businessData = businessDataMap.get(enterpriseId.toString());
            if (businessData == null) {
                logger.warn("No business data found for enterprise: {}", enterpriseId);
                return null;
            }

            List<Integer> businessIds = enterpriseToBusinessIdsMap.get(enterpriseId);
            if (CollectionUtils.isEmpty(businessIds)) {
                logger.warn("No business IDs found for enterprise: {}", enterpriseId);
                return null;
            }

            List<Integer> userList = new ArrayList<>();

            // Process each source (GMB, FACEBOOK) in parallel
            List<CompletableFuture<Void>> sourceFutures = Arrays.stream(sourceArray)
                .map(source -> CompletableFuture.runAsync(() ->
                    processSourceOptimized(source, enterpriseId, businessIds, userList, response)))
                .collect(Collectors.toList());

            // Wait for all source processing to complete
            CompletableFuture.allOf(sourceFutures.toArray(new CompletableFuture[0])).join();

            // Finalize user list
            List<Integer> finalUserList = userList.stream().distinct()
                .filter(num -> num != null && num != 0)
                .collect(Collectors.toList());
            response.setUsers(finalUserList);

            return response;

        } catch (Exception e) {
            logger.error("Exception while processing enterprise optimized: {} for enterprise: {}",
                e.getMessage(), enterpriseId, e);
            return null;
        }
    }

    /**
     * Optimized processing for a single source (GMB or FACEBOOK) for an enterprise
     */
    private void processSourceOptimized(String source, Long enterpriseId, List<Integer> businessIds,
            List<Integer> userList, EmailAlertResponse response) {

        try {
            logger.info("Processing source: {} for enterprise: {}", source, enterpriseId);

            // Fetch user IDs for this source and enterprise
            synchronized (userList) {
                userList.addAll(businessGetPageService.findIdsByEnterpriseIdAndStatusInAndChannel(
                    enterpriseId, Arrays.asList(Status.COMPLETE.getName()), source));
            }

            List<SocialPagesAudit> audits = new ArrayList<>();
            Integer useEsForFetchData = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getIntegerProperty("use.ES.to.fetch.data", 500);

            if (useEsForFetchData.equals(1)) {
                try {
                    List<SocialElasticDto> auditsFromEs = socialEsService.fetchAllMappedPagesForAccount(
                        Arrays.asList(source).stream().map(String::toLowerCase).toArray(String[]::new),
                        businessIds.stream().map(integer -> integer.toString()).collect(Collectors.toList()));
                    audits = generateSocialAuditPages(auditsFromEs, source);
                } catch (Exception e) {
                    logger.error("ES breaking while fetching data for email: {}", e.getMessage());
                    audits = fetchSocialPageData(source, businessIds);
                }
            } else {
                audits = fetchSocialPageData(source, businessIds);
            }

            if(CollectionUtils.isEmpty(audits)) {
                logger.info("No audits found for source: {} and enterprise: {}", source, enterpriseId);
                return;
            }

            EmailAlertDetailResponse res = new EmailAlertDetailResponse();
            res.setIsSocial(true);

            // Process audits and generate response details
            processAuditsAndGenerateResponse(audits, source, res, enterpriseId);

            synchronized (response) {
                response.getDetails().add(res);
            }

        } catch (Exception e) {
            logger.error("Exception while processing source: {} for enterprise: {}",
                source, enterpriseId, e);
        }
    }

    /**
     * Process audits and generate response details for a source
     */
    private void processAuditsAndGenerateResponse(List<SocialPagesAudit> audits, String source,
            EmailAlertDetailResponse res, Long enterpriseId) {

        if (CollectionUtils.isNotEmpty(audits)) {
            List<Integer> totalIds = new ArrayList<>();
            List<Integer> brokenIds = new ArrayList<>();

            audits.stream().forEach((audit) -> {
                if (audit.getBusinessId() != null) {
                    totalIds.add(audit.getBusinessId());
                    if (audit.getIsValid() == 0) {
                        brokenIds.add(audit.getBusinessId());
                    }
                }
            });

            res.setBrokenIds(brokenIds);
            res.setBroken(brokenIds.size());
            res.setTotalIds(totalIds);
            res.setTotal(totalIds.size());
            res.setIntegrationSource(source.equals("GMB") ? "Google" :
                source.substring(0, 1).toUpperCase() + source.substring(1).toLowerCase());
            res.setIntegrationSourceDisplayName(source.equals("GMB") ? "Google" :
                source.substring(0, 1).toUpperCase() + source.substring(1).toLowerCase());

            // Generate reconnect link - simplified for optimization
            try {
                GenerateRequest generateRequest = new GenerateRequest();
                generateRequest.setEnterpriseId(enterpriseId);
                generateRequest.setSource(OpenUrlSourceEnum.API.getName());
                res.setReconnectLink(openUrlService.generateUniqueUrl(source.toLowerCase(), generateRequest).getUrl());
            } catch (Exception e) {
                logger.warn("Failed to generate reconnect link for source: {} and enterprise: {}", source, enterpriseId);
                res.setReconnectLink("");
            }

            logger.info("Total no of businesses: {}, broken business: {} for source: {} and enterprise: {}",
                    totalIds.size(), brokenIds.size(), source, enterpriseId);
        }
    }

    @Override
    public void submitInitEmailForBrokenIntegration() {
    	logger.info("Submitting event to fetch all enterprises with broken integration");
    	producer.sendObject(Constants.INT_EMAIL_BROKEN_INTEGRATIONS, null) ;
    }

    @Async
    @Override
    public void initEmailForBrokenIntegration() throws IOException {
        logger.info("Initiating fetching broken integration data for enterprises fetched from DB");
        List<Long> enterpriseIdList = new ArrayList<>();
        Integer useEsForFetchData = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("use.ES.to.fetch.data", 500);
        if(useEsForFetchData.equals(1)) {
            try {
                List<SocialElasticDto> data = socialEsService.fetchAllIntegratedDisconnectedPages(Arrays.asList(sourceArray).stream().map(String::toLowerCase).toArray(String[]::new));
                List<String> enterpriseIds = data.stream().map(SocialElasticDto::getEnterprise_id).collect(Collectors.toList());
                enterpriseIdList = enterpriseIds.stream().map(s -> Long.parseLong(s)).collect(Collectors.toList());
                enterpriseIdList = enterpriseIdList.stream().distinct().collect(Collectors.toList());
            } catch (Exception e) {
                logger.error("Error in fetch data from ES in fetching broken integration: {}", e.getMessage());
                enterpriseIdList = fetchBrokenEnterpriseIds(true);
            }
        } else {
            enterpriseIdList = fetchBrokenEnterpriseIds(true);
        }

        logger.info("broken account count fetched inside initEmailForBrokenIntegration {}",enterpriseIdList.size());

        // TODO: make account level parallel

        enterpriseIdList.stream().forEach(businessId -> {
           // Map<String, Long> res = new HashMap<String, Long>();
           // res.put("enterpriseId",businessId);
            long startTime = System.currentTimeMillis();
            getAllUserDataForEmail(businessId, SocialAlertTypeEnum.WEEKLY.name());
            logger.info("total time take for account id {} is :  {}",businessId,System.currentTimeMillis()-startTime);

            //producer.sendWithKey(Constants.INIT_EMAIL_ALERT_BATCHES, businessId.toString(), res);
        });
    }
    
	@Override
	public List<EmailAlertDetailResponse> getIntegrationData(Long parentBusinessNumber, List<Integer> businessIds) {
		List<EmailAlertDetailResponse> integrationDetail = new ArrayList<EmailAlertDetailResponse>();
		GenerateRequest generateRequest = new GenerateRequest();
		generateRequest.setEnterpriseId(parentBusinessNumber);
		generateRequest.setSource(OpenUrlSourceEnum.API.getName());
		CompletableFuture<SocialFBPageRepository.IntegrationSummary> fbIntegrationSummaryFuture = CompletableFuture.supplyAsync(() -> {
			return facebookPageService.getInValidAndTotalCount(businessIds);
		});

		CompletableFuture<BusinessGMBLocationRawRepository.IntegrationSummary> gmbIntegrationSummaryFuture = CompletableFuture.supplyAsync(() -> {
			return gmbLocationDetailService.getInValidAndTotalCount(businessIds);
		});

		CompletableFuture<String> gmbCTAFuture = CompletableFuture.supplyAsync(() -> {
			return openUrlService.generateUniqueUrl(SocialChannel.GMB.getName(), generateRequest).getUrl();
		});
		CompletableFuture<String> fbCTAFuture = CompletableFuture.supplyAsync(() -> {
			return openUrlService.generateUniqueUrl(SocialChannel.FACEBOOK.getName(), generateRequest).getUrl();
		});
		CompletableFuture<Void> integrationCompletionFuture = CompletableFuture.allOf(fbIntegrationSummaryFuture,
				gmbIntegrationSummaryFuture, gmbCTAFuture, fbCTAFuture);
		try {
			integrationCompletionFuture.get(100, TimeUnit.SECONDS);

			EmailAlertDetailResponse integrationDetailGMB = new EmailAlertDetailResponse();
			BusinessGMBLocationRawRepository.IntegrationSummary gmbIntegrationSummary = gmbIntegrationSummaryFuture.get();
			integrationDetailGMB.setTotal(gmbIntegrationSummary.getTotal());
			integrationDetailGMB.setBroken(gmbIntegrationSummary.getInvalid());
			if (gmbIntegrationSummary.getInvalid() > 0) {
				integrationDetailGMB.setReconnectLink(gmbCTAFuture.get());
			}
			integrationDetailGMB.setIntegrationSource("Google");
			integrationDetailGMB.setIntegrationSourceDisplayName("Google");
			integrationDetailGMB.setIsSocial(true);

			EmailAlertDetailResponse integrationDetailFB = new EmailAlertDetailResponse();
			SocialFBPageRepository.IntegrationSummary fbIntegrationSummary = fbIntegrationSummaryFuture.get();

			integrationDetailFB.setTotal(fbIntegrationSummary.getTotal());
			integrationDetailFB.setBroken(fbIntegrationSummary.getInvalid());
			if (fbIntegrationSummary.getInvalid() > 0) {
				integrationDetailFB.setReconnectLink(fbCTAFuture.get());
			}
			integrationDetailFB.setIntegrationSource(SocialChannel.FACEBOOK.getLabel());
            integrationDetailFB.setIntegrationSourceDisplayName(SocialChannel.FACEBOOK.getLabel());
			integrationDetailFB.setIsSocial(true);

			integrationDetail.add(integrationDetailGMB);
			integrationDetail.add(integrationDetailFB);


		} catch (InterruptedException | ExecutionException | TimeoutException e) {

			logger.info("Error occurred in fetching integration details for business acccount {} :{}",
					parentBusinessNumber, e);
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT,
					"Error Occured while fetching integration data");

		}
		return integrationDetail;
	}
}
