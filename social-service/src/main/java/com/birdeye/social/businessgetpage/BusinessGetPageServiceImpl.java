package com.birdeye.social.businessgetpage;

import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class BusinessGetPageServiceImpl implements IBusinessGetPageService {

    @Autowired
    private BusinessGetPageReqRepo businessGetPageReqRepo;

    @Override
    public BusinessGetPageRequest save(BusinessGetPageRequest businessGetPageRequest) {
        return businessGetPageReqRepo.save(businessGetPageRequest);
    }

    @Override
    public List<BusinessGetPageRequest> saveAll(List<BusinessGetPageRequest> businessGetPageRequest) {
        return businessGetPageReqRepo.save(businessGetPageRequest);
    }

    @Override
    public BusinessGetPageRequest saveAndFlush(BusinessGetPageRequest businessGetPageRequest) {
        return businessGetPageReqRepo.saveAndFlush(businessGetPageRequest);
    }

    @Override
    public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusInAndChannel(Long businessId, List<String> statusList, String channel) {
        return businessGetPageReqRepo.findByEnterpriseIdAndStatusInAndChannel(businessId,statusList,channel);
    }

    @Override
    public List<Integer> findIdsByEnterpriseIdAndStatusInAndChannel(Long businessId, List<String> statusList, String channel) {
        return businessGetPageReqRepo.findIdsByEnterpriseIdAndStatusInAndChannel(businessId,channel,statusList);
    }

    @Override
    public Map<String, List<Integer>> findIdsByEnterpriseIdsAndStatusInAndChannelsBulk(List<Long> enterpriseIds, List<String> channels, List<String> statusList) {
        if (CollectionUtils.isEmpty(enterpriseIds) || CollectionUtils.isEmpty(channels)) {
            return new HashMap<>();
        }

        List<Object[]> results = businessGetPageReqRepo.findUserIdsByEnterpriseIdsAndChannelsGrouped(enterpriseIds, channels, statusList);
        Map<String, List<Integer>> resultMap = new HashMap<>();

        for (Object[] result : results) {
            Long enterpriseId = (Long) result[0];
            String channel = (String) result[1];
            Integer userId = (Integer) result[2];

            if (userId != null) {
                String key = enterpriseId + "_" + channel;
                resultMap.computeIfAbsent(key, k -> new ArrayList<>()).add(userId);
            }
        }

        return resultMap;
    }

    @Override
    public List<Integer> findDistinctIdsByEnterpriseIdsAndStatusInAndChannel(List<Long> enterpriseIds, String channel, List<String> statusList) {
        if (CollectionUtils.isEmpty(enterpriseIds)) {
            return new ArrayList<>();
        }

        return businessGetPageReqRepo.findDistinctIdsByEnterpriseIdsAndStatusInAndChannel(enterpriseIds, channel, statusList);
    }

    @Override
    public Map<String, List<Integer>> findIdsByEnterpriseIdAndChannelsBulk(Long enterpriseId, List<String> channels, List<String> statusList) {
        if (enterpriseId == null || CollectionUtils.isEmpty(channels)) {
            return new HashMap<>();
        }

        List<Object[]> results = businessGetPageReqRepo.findUserIdsByEnterpriseIdsAndChannelsGrouped(
            List.of(enterpriseId), channels, statusList);
        Map<String, List<Integer>> resultMap = new HashMap<>();

        for (Object[] result : results) {
            String channel = (String) result[1];
            Integer userId = (Integer) result[2];

            if (userId != null) {
                resultMap.computeIfAbsent(channel, k -> new ArrayList<>()).add(userId);
            }
        }

        return resultMap;
    }



    @Override
    public List<BusinessGetPageRequest> findByEnterpriseIdAndStatusInAndChannelAndRequestType(Long businessId, List<String> statusList, String channel, String requestType) {
        return businessGetPageReqRepo.findByEnterpriseIdAndStatusInAndChannelAndRequestType(businessId,statusList,channel,requestType);
    }

    @Override
    public List<BusinessGetPageRequest> getRequestForBusiness(Long enterpriseId, String status, String channel, String requestType) {
        return businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(enterpriseId, status, channel, requestType);
    }

    @Override
    public BusinessGetPageRequest findLastRequestByEnterpriseIdAndChannelAndRequestType(Long businessId, String channel, String requestType) {
        List <BusinessGetPageRequest> businessGetPageRequests = businessGetPageReqRepo.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, channel, requestType);
        if (CollectionUtils.isNotEmpty(businessGetPageRequests)) {
            return businessGetPageRequests.get(0);
        } else {
            return null;
        }
    }

    @Override
    public BusinessGetPageRequest findLastRequestByResellerIdAndChannelAndRequestType(Long resellerId, String channel, String requestType) {
        BusinessGetPageRequest businessGetPageRequest = businessGetPageReqRepo.findLastRequestByResellerIdAndChannelAndRequestType(resellerId, channel, requestType);
        return businessGetPageRequest;
    }

    @Override
    public BusinessGetPageRequest findLastRequestByEnterpriseIdAndChannel(Long businessId, String channel) {
        List <BusinessGetPageRequest> gmbData = businessGetPageReqRepo.findLastRequestByEnterpriseIdAndChannel(businessId, channel);
        if (CollectionUtils.isNotEmpty(gmbData)) {
            return gmbData.get(0);
        } else {
            return null;
        }
    }

    @Override
    public BusinessGetPageRequest findLastRequestByResellerIdAndChannel(Long resellerId, String channel) {
        List<BusinessGetPageRequest> data = businessGetPageReqRepo.findLastRequestByResellerIdIdAndChannel(resellerId, channel);
        if (CollectionUtils.isNotEmpty(data)) {
            return data.get(0);
        } else {
            return null;
//            throw new BirdeyeSocialException("Not able to get business get page request");
        }
    }

    @Override
    public BusinessGetPageRequest findByFreemiumSessionId(Integer sessionId){
        BusinessGetPageRequest data = businessGetPageReqRepo.findByFreemiumSessionId(sessionId);
        if (Objects.nonNull(data)) {
            return data;
        } else {
            return null;
//            throw new BirdeyeSocialException("Not able to get business get page request");
        }
    }


    /**
     * This method will put a lock on row , SELECT for update
     * @param id
     * @return
     */
    @Override
    public BusinessGetPageRequest findById(String id) {
        return businessGetPageReqRepo.fetchById(Integer.valueOf(id));
    }

    @Override
    public BusinessGetPageRequest findOne(Integer id) {
        return businessGetPageReqRepo.findOne(id);
    }

    @Override
    @Transactional
    public Integer fetchUserFromBusinessGetPageRequestId(Integer id){
        return businessGetPageReqRepo.findBirdeyeUserIdById(id);
    }

    @Override
    public List<BusinessGetPageRequest> findAllById(List<Integer> requestIds) {

        return businessGetPageReqRepo.findAll(requestIds);
    }

    @Override
    public void updateAll(List<Integer> requests,String status,String errorLog) {
        businessGetPageReqRepo.updateByRequestIds(requests,status,errorLog);
    }

	@Override
	public List<BusinessGetPageRequest> findByResellerIdAndStatusInAndChannel(Long businessId, List<String> statusList,
			String channel) {
        return businessGetPageReqRepo.findByResellerIdAndStatusInAndChannel(businessId,statusList,channel);

	}


}
